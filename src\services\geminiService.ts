import axios from 'axios';
import type { AxiosInstance, AxiosResponse } from 'axios';
import type {
  AIAnalysisRequest,
  AIAnalysisResponse,
  OptionsChainData,
  GeminiResponse
} from '../types';
import { API_CONFIG, AI_PROMPTS, ERROR_MESSAGES, UI_CONFIG } from '../utils/constants';

class GeminiService {
  private api: AxiosInstance;
  private apiKey: string;

  constructor() {
    this.apiKey = API_CONFIG.GEMINI.API_KEY || '';

    this.api = axios.create({
      baseURL: API_CONFIG.GEMINI.BASE_URL || 'https://generativelanguage.googleapis.com/v1beta',
      timeout: UI_CONFIG.TIMEOUT * 3, // Longer timeout for AI processing
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        console.log('Gemini API Request:', config.url);
        return config;
      },
      (error) => {
        console.error('Gemini API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response) => {
        console.log('Gemini API Response received');
        return response;
      },
      (error) => {
        console.error('Gemini API Response Error:', error);
        if (!error.response) {
          throw new Error(ERROR_MESSAGES.NETWORK_ERROR);
        }
        throw new Error(ERROR_MESSAGES.AI_ERROR);
      }
    );
  }

  /**
   * Check if service is properly configured
   */
  isConfigured(): boolean {
    return !!this.apiKey;
  }

  /**
   * Analyze options chain data and provide strike recommendations
   */
  async analyzeOptionsChain(request: AIAnalysisRequest): Promise<AIAnalysisResponse> {
    if (!this.isConfigured()) {
      throw new Error('Gemini API is not properly configured. Please check your environment variables.');
    }
    try {
      const prompt = this.buildAnalysisPrompt(request);
      const response = await this.generateContent(prompt);
      return this.parseAnalysisResponse(response);
    } catch (error) {
      console.error('Error analyzing options chain:', error);
      throw error;
    }
  }

  /**
   * Get strike price recommendations
   */
  async getStrikeRecommendations(optionsData: OptionsChainData): Promise<AIAnalysisResponse> {
    const request: AIAnalysisRequest = {
      optionsData,
      analysisType: 'STRIKE_SELECTION',
    };

    return this.analyzeOptionsChain(request);
  }

  /**
   * Get risk assessment for specific positions
   */
  async getRiskAssessment(
    optionsData: OptionsChainData,
    userPreferences?: AIAnalysisRequest['userPreferences']
  ): Promise<AIAnalysisResponse> {
    const request: AIAnalysisRequest = {
      optionsData,
      analysisType: 'RISK_ASSESSMENT',
      userPreferences,
    };

    return this.analyzeOptionsChain(request);
  }

  /**
   * Get strategy recommendations
   */
  async getStrategyRecommendations(optionsData: OptionsChainData): Promise<AIAnalysisResponse> {
    const request: AIAnalysisRequest = {
      optionsData,
      analysisType: 'STRATEGY_RECOMMENDATION',
    };

    return this.analyzeOptionsChain(request);
  }

  /**
   * Generate content using Gemini API
   */
  private async generateContent(prompt: string): Promise<string> {
    try {
      const response: AxiosResponse<GeminiResponse> = await this.api.post(
        `/models/gemini-2.0-flash-exp:generateContent?key=${this.apiKey}`,
        {
          contents: [
            {
              parts: [
                {
                  text: prompt,
                },
              ],
            },
          ],
          generationConfig: {
            temperature: 0.1, // Low temperature for consistent analysis
            topK: 1,
            topP: 0.8,
            maxOutputTokens: 4096,
          },
          safetySettings: [
            {
              category: 'HARM_CATEGORY_HARASSMENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE',
            },
            {
              category: 'HARM_CATEGORY_HATE_SPEECH',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE',
            },
            {
              category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE',
            },
            {
              category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE',
            },
          ],
        }
      );

      if (!response.data.candidates || response.data.candidates.length === 0) {
        throw new Error('No response from AI model');
      }

      const content = response.data.candidates[0].content.parts[0].text;
      if (!content) {
        throw new Error('Empty response from AI model');
      }

      return content;
    } catch (error) {
      console.error('Error generating content:', error);
      throw new Error(ERROR_MESSAGES.AI_ERROR);
    }
  }

  /**
   * Build analysis prompt based on request type
   */
  private buildAnalysisPrompt(request: AIAnalysisRequest): string {
    let basePrompt = '';

    switch (request.analysisType) {
      case 'STRIKE_SELECTION':
        basePrompt = AI_PROMPTS.STRIKE_SELECTION;
        break;
      case 'RISK_ASSESSMENT':
        basePrompt = AI_PROMPTS.RISK_ASSESSMENT;
        break;
      case 'STRATEGY_RECOMMENDATION':
        basePrompt = AI_PROMPTS.STRATEGY_RECOMMENDATION;
        break;
      default:
        basePrompt = AI_PROMPTS.STRIKE_SELECTION;
    }

    // Add user preferences if provided
    if (request.userPreferences) {
      basePrompt += `\n\nUSER PREFERENCES:
Risk Tolerance: ${request.userPreferences.riskTolerance}
Time Horizon: ${request.userPreferences.timeHorizon} days
Maximum Loss: ₹${request.userPreferences.maxLoss}
Target Profit: ₹${request.userPreferences.targetProfit}`;
    }

    // Add options data
    basePrompt += `\n\n${JSON.stringify(request.optionsData, null, 2)}`;

    return basePrompt;
  }

  /**
   * Parse AI response and extract structured data
   */
  private parseAnalysisResponse(response: string): AIAnalysisResponse {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsedResponse = JSON.parse(jsonMatch[0]);

      // Validate response structure
      if (!parsedResponse.recommendations || !Array.isArray(parsedResponse.recommendations)) {
        throw new Error('Invalid response structure');
      }

      // Ensure all required fields are present
      const analysisResponse: AIAnalysisResponse = {
        recommendations: parsedResponse.recommendations.map((rec: Record<string, unknown>) => ({
          strikePrice: (rec.strikePrice as number) || 0,
          optionType: (rec.optionType as 'CE' | 'PE') || 'CE',
          confidence: (rec.confidence as number) || 0,
          reasoning: (rec.reasoning as string) || '',
          entryPrice: (rec.entryPrice as number) || 0,
          targetPrice: (rec.targetPrice as number) || 0,
          stopLoss: (rec.stopLoss as number) || 0,
          riskReward: (rec.riskReward as number) || 0,
          maxLoss: (rec.maxLoss as number) || 0,
          maxProfit: (rec.maxProfit as number) || 0,
          breakeven: (rec.breakeven as number) || 0,
        })),
        marketOutlook: parsedResponse.marketOutlook || 'Neutral',
        riskAssessment: parsedResponse.riskAssessment || 'Medium risk',
        keyInsights: parsedResponse.keyInsights || [],
        warnings: parsedResponse.warnings || [],
        timestamp: new Date().toISOString(),
      };

      return analysisResponse;
    } catch (error) {
      console.error('Error parsing AI response:', error);
      
      // Fallback: Create a basic response from the raw text
      return {
        recommendations: [],
        marketOutlook: 'Unable to parse detailed analysis',
        riskAssessment: response.substring(0, 500) + '...',
        keyInsights: ['AI analysis completed but response format needs adjustment'],
        warnings: ['Please review the raw analysis output'],
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Test API connection
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.generateContent('Test connection. Respond with "OK".');
      return response.toLowerCase().includes('ok');
    } catch (error) {
      console.error('Gemini API connection test failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const geminiService = new GeminiService();
export default geminiService;
