import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
} from '@mui/material';
import {
  ExpandMore,
  TrendingUp,
  Warning,
  Lightbulb,
  GpsFixed as Target,
  Security,
  Analytics,
  Info,
} from '@mui/icons-material';
import type { AIAnalysisResponse, OptionsChainData, StrikeRecommendation } from '../types';

interface AIAnalysisProps {
  analysis: AIAnalysisResponse;
  optionsData: OptionsChainData | null;
}

const AIAnalysis: React.FC<AIAnalysisProps> = ({ analysis }) => {
  const [selectedRecommendation, setSelectedRecommendation] = useState<StrikeRecommendation | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);

  // Handle recommendation click
  const handleRecommendationClick = (recommendation: StrikeRecommendation) => {
    setSelectedRecommendation(recommendation);
    setDetailsOpen(true);
  };

  // Close details dialog
  const handleCloseDetails = () => {
    setDetailsOpen(false);
    setSelectedRecommendation(null);
  };

  // Get confidence color
  const getConfidenceColor = (confidence: number): 'success' | 'warning' | 'error' => {
    if (confidence >= 80) return 'success';
    if (confidence >= 60) return 'warning';
    return 'error';
  };

  // Get risk-reward color
  const getRiskRewardColor = (ratio: number): 'success' | 'warning' | 'error' => {
    if (ratio >= 2) return 'success';
    if (ratio >= 1.5) return 'warning';
    return 'error';
  };

  // Format currency
  const formatCurrency = (value: number): string => {
    return `₹${value.toFixed(2)}`;
  };

  return (
    <Box>
      {/* Header */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Analytics sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6">
              AI Analysis Results
            </Typography>
          </Box>
          
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Generated: {new Date(analysis.timestamp).toLocaleString()}
          </Typography>
          
          <Chip
            label={`${analysis.recommendations.length} Recommendations`}
            color="primary"
            variant="outlined"
          />
        </CardContent>
      </Card>

      {/* Market Outlook */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Market Outlook
          </Typography>
          <Alert 
            severity="info" 
            icon={<TrendingUp />}
            sx={{ mb: 2 }}
          >
            {analysis.marketOutlook}
          </Alert>
        </CardContent>
      </Card>

      {/* Strike Recommendations */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Strike Recommendations
          </Typography>
          
          {analysis.recommendations.length === 0 ? (
            <Alert severity="warning">
              No specific strike recommendations available
            </Alert>
          ) : (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {analysis.recommendations.map((rec, index) => (
                <Card 
                  key={index} 
                  variant="outlined" 
                  sx={{ 
                    cursor: 'pointer',
                    '&:hover': { bgcolor: 'action.hover' }
                  }}
                  onClick={() => handleRecommendationClick(rec)}
                >
                  <CardContent sx={{ pb: '16px !important' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Box>
                        <Typography variant="h6" component="div">
                          {rec.strikePrice} {rec.optionType}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Entry: {formatCurrency(rec.entryPrice)}
                        </Typography>
                      </Box>
                      <Box sx={{ textAlign: 'right' }}>
                        <Chip
                          label={`${rec.confidence}% Confidence`}
                          color={getConfidenceColor(rec.confidence)}
                          size="small"
                          sx={{ mb: 1 }}
                        />
                        <br />
                        <Chip
                          label={`R:R ${rec.riskReward.toFixed(1)}`}
                          color={getRiskRewardColor(rec.riskReward)}
                          size="small"
                          variant="outlined"
                        />
                      </Box>
                    </Box>
                    
                    <Typography variant="body2" sx={{ mb: 2 }}>
                      {rec.reasoning.substring(0, 150)}...
                    </Typography>
                    
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Chip
                        icon={<Target />}
                        label={`Target: ${formatCurrency(rec.targetPrice)}`}
                        size="small"
                        color="success"
                        variant="outlined"
                      />
                      <Chip
                        icon={<Security />}
                        label={`SL: ${formatCurrency(rec.stopLoss)}`}
                        size="small"
                        color="error"
                        variant="outlined"
                      />
                    </Box>
                  </CardContent>
                </Card>
              ))}
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Expandable Sections */}
      <Box sx={{ mb: 2 }}>
        {/* Risk Assessment */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Security sx={{ mr: 1, color: 'warning.main' }} />
              <Typography variant="h6">Risk Assessment</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body2" paragraph>
              {analysis.riskAssessment}
            </Typography>
          </AccordionDetails>
        </Accordion>

        {/* Key Insights */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Lightbulb sx={{ mr: 1, color: 'info.main' }} />
              <Typography variant="h6">Key Insights</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <List dense>
              {analysis.keyInsights.map((insight, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <Info color="primary" />
                  </ListItemIcon>
                  <ListItemText primary={insight} />
                </ListItem>
              ))}
            </List>
          </AccordionDetails>
        </Accordion>

        {/* Warnings */}
        {analysis.warnings.length > 0 && (
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Warning sx={{ mr: 1, color: 'error.main' }} />
                <Typography variant="h6">Warnings</Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {analysis.warnings.map((warning, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <Warning color="error" />
                    </ListItemIcon>
                    <ListItemText primary={warning} />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>
        )}
      </Box>

      {/* Recommendation Details Dialog */}
      <Dialog
        open={detailsOpen}
        onClose={handleCloseDetails}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">
              {selectedRecommendation?.strikePrice} {selectedRecommendation?.optionType} - Details
            </Typography>
            <Chip
              label={`${selectedRecommendation?.confidence}% Confidence`}
              color={selectedRecommendation ? getConfidenceColor(selectedRecommendation.confidence) : 'default'}
            />
          </Box>
        </DialogTitle>
        
        <DialogContent>
          {selectedRecommendation && (
            <Box>
              {/* Reasoning */}
              <Typography variant="h6" gutterBottom>
                Analysis Reasoning
              </Typography>
              <Typography variant="body2" paragraph>
                {selectedRecommendation.reasoning}
              </Typography>
              
              <Divider sx={{ my: 2 }} />
              
              {/* Trade Details */}
              <Typography variant="h6" gutterBottom>
                Trade Details
              </Typography>
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableBody>
                    <TableRow>
                      <TableCell><strong>Entry Price</strong></TableCell>
                      <TableCell>{formatCurrency(selectedRecommendation.entryPrice)}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell><strong>Target Price</strong></TableCell>
                      <TableCell sx={{ color: 'success.main' }}>
                        {formatCurrency(selectedRecommendation.targetPrice)}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell><strong>Stop Loss</strong></TableCell>
                      <TableCell sx={{ color: 'error.main' }}>
                        {formatCurrency(selectedRecommendation.stopLoss)}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell><strong>Breakeven</strong></TableCell>
                      <TableCell>{formatCurrency(selectedRecommendation.breakeven)}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell><strong>Max Profit</strong></TableCell>
                      <TableCell sx={{ color: 'success.main' }}>
                        {formatCurrency(selectedRecommendation.maxProfit)}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell><strong>Max Loss</strong></TableCell>
                      <TableCell sx={{ color: 'error.main' }}>
                        {formatCurrency(selectedRecommendation.maxLoss)}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell><strong>Risk:Reward Ratio</strong></TableCell>
                      <TableCell>
                        <Chip
                          label={`1:${selectedRecommendation.riskReward.toFixed(1)}`}
                          color={getRiskRewardColor(selectedRecommendation.riskReward)}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleCloseDetails}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AIAnalysis;
