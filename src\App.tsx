import React, { useState } from 'react';
import {
  Theme<PERSON>rovider,
  createTheme,
  CssBaseline,
  Container,
  AppBar,
  Toolbar,
  Typography,
  Box,
  Alert,
  Snackbar
} from '@mui/material';
import { TrendingUp } from '@mui/icons-material';
import Dashboard from './components/Dashboard';
import type { AppState, StockSymbol, OptionsChainData, AIAnalysisResponse } from './types';
import { SUCCESS_MESSAGES } from './utils/constants';

// Create Material-UI theme
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    h4: {
      fontWeight: 600,
    },
    h6: {
      fontWeight: 500,
    },
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          borderRadius: 12,
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 500,
        },
      },
    },
  },
});

function App() {
  const [appState, setAppState] = useState<AppState>({
    selectedStock: null,
    optionsData: null,
    aiAnalysis: null,
    loading: {
      optionsData: false,
      aiAnalysis: false,
    },
    error: {
      optionsData: null,
      aiAnalysis: null,
    },
  });

  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });

  // Handle stock selection
  const handleStockSelect = (stock: StockSymbol) => {
    setAppState(prev => ({
      ...prev,
      selectedStock: stock,
      optionsData: null,
      aiAnalysis: null,
      error: {
        optionsData: null,
        aiAnalysis: null,
      },
    }));
  };

  // Handle options data loading
  const handleOptionsDataLoad = (data: OptionsChainData) => {
    setAppState(prev => ({
      ...prev,
      optionsData: data,
      error: {
        ...prev.error,
        optionsData: null,
      },
    }));

    showSnackbar(SUCCESS_MESSAGES.DATA_LOADED, 'success');
  };

  // Handle AI analysis completion
  const handleAIAnalysisComplete = (analysis: AIAnalysisResponse) => {
    setAppState(prev => ({
      ...prev,
      aiAnalysis: analysis,
      error: {
        ...prev.error,
        aiAnalysis: null,
      },
    }));

    showSnackbar(SUCCESS_MESSAGES.ANALYSIS_COMPLETE, 'success');
  };

  // Handle loading states
  const handleLoadingChange = (type: 'optionsData' | 'aiAnalysis', loading: boolean) => {
    setAppState(prev => ({
      ...prev,
      loading: {
        ...prev.loading,
        [type]: loading,
      },
    }));
  };

  // Handle errors
  const handleError = (type: 'optionsData' | 'aiAnalysis', error: string) => {
    setAppState(prev => ({
      ...prev,
      error: {
        ...prev.error,
        [type]: error,
      },
      loading: {
        ...prev.loading,
        [type]: false,
      },
    }));

    showSnackbar(error, 'error');
  };

  // Show snackbar notification
  const showSnackbar = (message: string, severity: 'success' | 'error' | 'warning' | 'info') => {
    setSnackbar({
      open: true,
      message,
      severity,
    });
  };

  // Close snackbar
  const handleSnackbarClose = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ flexGrow: 1, minHeight: '100vh', backgroundColor: 'background.default' }}>
        {/* App Bar */}
        <AppBar position="static" elevation={0}>
          <Toolbar>
            <TrendingUp sx={{ mr: 2 }} />
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              Options Chain Analyzer
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.8 }}>
              AI-Powered Strike Selection
            </Typography>
          </Toolbar>
        </AppBar>

        {/* Main Content */}
        <Container maxWidth="xl" sx={{ mt: 3, mb: 3 }}>
          <Dashboard
            appState={appState}
            onStockSelect={handleStockSelect}
            onOptionsDataLoad={handleOptionsDataLoad}
            onAIAnalysisComplete={handleAIAnalysisComplete}
            onLoadingChange={handleLoadingChange}
            onError={handleError}
          />
        </Container>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleSnackbarClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={handleSnackbarClose}
            severity={snackbar.severity}
            variant="filled"
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </ThemeProvider>
  );
}

export default App;
