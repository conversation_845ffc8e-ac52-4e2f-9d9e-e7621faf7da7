import axios from 'axios';
import type { AxiosInstance, AxiosResponse } from 'axios';
import type {
  FyersAuthConfig,
  OptionsChainData,
  OptionData,
} from '../types';
import { API_CONFIG, ERROR_MESSAGES, UI_CONFIG } from '../utils/constants';

class FyersService {
  private api: AxiosInstance;
  private config: FyersAuthConfig;

  constructor() {
    this.config = {
      appId: API_CONFIG.FYERS.APP_ID || '',
      secretId: API_CONFIG.FYERS.SECRET_ID || '',
      accessToken: API_CONFIG.FYERS.ACCESS_TOKEN || '',
      baseUrl: API_CONFIG.FYERS.BASE_URL || 'https://api-t1.fyers.in/api/v3',
    };

    this.api = axios.create({
      baseURL: this.config.baseUrl,
      timeout: UI_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `${this.config.appId}:${this.config.accessToken}`,
      },
    });

    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        console.log('Fyers API Request:', config.url);
        return config;
      },
      (error) => {
        console.error('Fyers API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response) => {
        console.log('Fyers API Response:', response.data);
        return response;
      },
      (error) => {
        console.error('Fyers API Response Error:', error);
        if (error.response?.status === 401) {
          throw new Error(ERROR_MESSAGES.INVALID_TOKEN);
        }
        if (!error.response) {
          throw new Error(ERROR_MESSAGES.NETWORK_ERROR);
        }
        throw new Error(ERROR_MESSAGES.API_ERROR);
      }
    );
  }

  /**
   * Check if service is properly configured
   */
  isConfigured(): boolean {
    return !!(this.config.appId && this.config.accessToken);
  }

  /**
   * Update access token
   */
  updateAccessToken(token: string): void {
    this.config.accessToken = token;
    this.api.defaults.headers['Authorization'] = `${this.config.appId}:${token}`;
  }

  /**
   * Get user profile to verify authentication
   */
  async getProfile(): Promise<Record<string, unknown>> {
    if (!this.isConfigured()) {
      throw new Error('Fyers API is not properly configured. Please check your environment variables.');
    }

    try {
      const response: AxiosResponse = await this.api.get('/user/profile');
      return response.data;
    } catch (error) {
      console.error('Error fetching profile:', error);
      throw error;
    }
  }



  /**
   * Get options chain data for a symbol
   * Updated for Fyers API v3 - Based on official documentation
   */
  async getOptionsChain(symbol: string, expiryDate?: string): Promise<OptionsChainData> {
    if (!this.isConfigured()) {
      throw new Error('Fyers API is not properly configured. Please check your environment variables.');
    }

    try {
      // Format symbol for Fyers API (e.g., NSE:NIFTY50-INDEX, NSE:RELIANCE-EQ)
      const formattedSymbol = this.formatSymbol(symbol);

      // First get the current price
      const currentPrice = await this.getQuote(symbol);

      // Get options chain using POST method as per Fyers API v3
      const requestData = {
        symbol: formattedSymbol,
        ...(expiryDate && { expiry: expiryDate })
      };

      const response: AxiosResponse = await this.api.post('/optchain', requestData);

      if (response.data.s !== 'ok' || !response.data.d) {
        throw new Error(response.data.message || ERROR_MESSAGES.NO_DATA);
      }

      return this.transformOptionsChainData(response.data.d, symbol, currentPrice);
    } catch (error) {
      console.error('Error fetching options chain:', error);

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw new Error('Invalid access token. Please regenerate your Fyers access token.');
        }
        if (error.response?.status === 403) {
          throw new Error('Access forbidden. Please check your API permissions.');
        }
      }

      throw error;
    }
  }

  /**
   * Get current market price for a symbol
   * Updated for Fyers API v3 - Based on official documentation
   */
  async getQuote(symbol: string): Promise<number> {
    if (!this.isConfigured()) {
      throw new Error('Fyers API is not properly configured. Please check your environment variables.');
    }

    try {
      const formattedSymbol = this.formatSymbol(symbol);

      // Use POST method with data as per Fyers API v3 documentation
      const requestData = {
        symbols: formattedSymbol
      };

      const response: AxiosResponse = await this.api.post('/quotes', requestData);

      if (response.data.s !== 'ok' || !response.data.d) {
        throw new Error(response.data.message || ERROR_MESSAGES.NO_DATA);
      }

      // Parse response structure for v3
      const quoteData = response.data.d[formattedSymbol];
      return quoteData?.v?.lp || quoteData?.lp || 0;
    } catch (error) {
      console.error('Error fetching quote:', error);
      throw error;
    }
  }

  /**
   * Get historical data for analysis
   * Updated for Fyers API v3 - Based on official documentation
   */
  async getHistoricalData(
    symbol: string,
    resolution: string = '1D',
    fromDate: string,
    toDate: string
  ): Promise<Record<string, unknown>> {
    if (!this.isConfigured()) {
      throw new Error('Fyers API is not properly configured. Please check your environment variables.');
    }

    try {
      const formattedSymbol = this.formatSymbol(symbol);

      // Use POST method with data as per Fyers API v3 documentation
      const requestData = {
        symbol: formattedSymbol,
        resolution,
        date_format: '1',
        range_from: fromDate,
        range_to: toDate,
      };

      const response: AxiosResponse = await this.api.post('/history', requestData);

      return response.data;
    } catch (error) {
      console.error('Error fetching historical data:', error);
      throw error;
    }
  }

  /**
   * Format symbol for Fyers API v3
   */
  private formatSymbol(symbol: string): string {
    // Handle index symbols - Updated for v3
    if (symbol === 'NIFTY') {
      return 'NSE:NIFTY50-INDEX';
    }
    if (symbol === 'BANKNIFTY') {
      return 'NSE:BANKNIFTY-INDEX';
    }
    if (symbol === 'FINNIFTY') {
      return 'NSE:FINNIFTY-INDEX';
    }

    // Handle equity symbols
    return `NSE:${symbol}-EQ`;
  }

  /**
   * Transform Fyers API response to our data structure
   * Updated for v3 response format
   */
  private transformOptionsChainData(data: Record<string, unknown>, symbol: string, spotPrice: number): OptionsChainData {
    const callOptions: OptionData[] = [];
    const putOptions: OptionData[] = [];

    // Process options chain data - Updated for v3 format
    if (data && typeof data === 'object') {
      // v3 API returns data in a different structure
      Object.keys(data).forEach((key) => {
        const option = data[key] as Record<string, unknown>;
        if (option && typeof option === 'object') {
          const optionData: OptionData = {
            symbol: (option.fyToken as string) || key,
            strikePrice: (option.strikePrice as number) || 0,
            optionType: (option.optionType as string) === 'CE' ? 'CE' : 'PE',
            expiryDate: (option.expiry as string) || '',
            ltp: (option.ltp as number) || 0,
            bid: (option.bid as number) || 0,
            ask: (option.ask as number) || 0,
            volume: (option.volume as number) || 0,
            openInterest: (option.oi as number) || 0,
            impliedVolatility: (option.iv as number) || 0,
            delta: (option.delta as number) || 0,
            gamma: (option.gamma as number) || 0,
            theta: (option.theta as number) || 0,
            vega: (option.vega as number) || 0,
            rho: (option.rho as number) || 0,
            change: (option.ch as number) || 0,
            changePercent: (option.chp as number) || 0,
          };

          if ((option.optionType as string) === 'CE') {
            callOptions.push(optionData);
          } else if ((option.optionType as string) === 'PE') {
            putOptions.push(optionData);
          }
        }
      });
    }

    // Sort by strike price
    callOptions.sort((a, b) => a.strikePrice - b.strikePrice);
    putOptions.sort((a, b) => a.strikePrice - b.strikePrice);

    return {
      underlyingSymbol: symbol,
      underlyingPrice: spotPrice,
      expiryDate: callOptions[0]?.expiryDate || putOptions[0]?.expiryDate || '',
      callOptions,
      putOptions,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Get available expiry dates for a symbol
   * Updated for Fyers API v3 - Based on official documentation
   */
  async getExpiryDates(symbol: string): Promise<string[]> {
    if (!this.isConfigured()) {
      throw new Error('Fyers API is not properly configured. Please check your environment variables.');
    }

    try {
      const formattedSymbol = this.formatSymbol(symbol);

      // Use POST method with data as per Fyers API v3 documentation
      const requestData = {
        symbol: formattedSymbol,
        get_expiry: 1
      };

      const response: AxiosResponse = await this.api.post('/optchain', requestData);

      if (response.data.s !== 'ok' || !response.data.data) {
        return [];
      }

      return response.data.data.expiry_dates || [];
    } catch (error) {
      console.error('Error fetching expiry dates:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const fyersService = new FyersService();
export default fyersService;
