import axios from 'axios';
import type { AxiosInstance, AxiosResponse } from 'axios';
import type {
  FyersAuthConfig,
  OptionsChainData,
  OptionData,
} from '../types';
import { API_CONFIG, ERROR_MESSAGES, UI_CONFIG } from '../utils/constants';

class FyersService {
  private api: AxiosInstance;
  private config: FyersAuthConfig;

  constructor() {
    this.config = {
      appId: API_CONFIG.FYERS.APP_ID || '',
      secretId: API_CONFIG.FYERS.SECRET_ID || '',
      accessToken: API_CONFIG.FYERS.ACCESS_TOKEN || '',
      baseUrl: API_CONFIG.FYERS.BASE_URL || 'https://api-t1.fyers.in/api/v3',
    };

    this.api = axios.create({
      baseURL: this.config.baseUrl,
      timeout: UI_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `${this.config.appId}:${this.config.accessToken}`,
      },
    });

    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        console.log('Fyers API Request:', config.url);
        return config;
      },
      (error) => {
        console.error('Fyers API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response) => {
        console.log('Fyers API Response:', response.data);
        return response;
      },
      (error) => {
        console.error('Fyers API Response Error:', error);
        if (error.response?.status === 401) {
          throw new Error(ERROR_MESSAGES.INVALID_TOKEN);
        }
        if (!error.response) {
          throw new Error(ERROR_MESSAGES.NETWORK_ERROR);
        }
        throw new Error(ERROR_MESSAGES.API_ERROR);
      }
    );
  }

  /**
   * Check if service is properly configured
   */
  isConfigured(): boolean {
    return !!(this.config.appId && this.config.accessToken);
  }

  /**
   * Update access token
   */
  updateAccessToken(token: string): void {
    this.config.accessToken = token;
    this.api.defaults.headers['Authorization'] = `${this.config.appId}:${token}`;
  }

  /**
   * Get user profile to verify authentication
   */
  async getProfile(): Promise<Record<string, unknown>> {
    try {
      const response: AxiosResponse = await this.api.get('/user/profile');
      return response.data;
    } catch (error) {
      console.error('Error fetching profile:', error);
      throw error;
    }
  }

  /**
   * Test API connection with a simple call
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.getProfile();
      return response && typeof response === 'object';
    } catch (error) {
      console.error('API connection test failed:', error);
      return false;
    }
  }

  /**
   * Generate mock options data for testing
   */
  private generateMockOptionsData(symbol: string): OptionsChainData {
    const spotPrice = symbol === 'NIFTY' ? 24500 : symbol === 'BANKNIFTY' ? 52000 : 3500;
    const callOptions: OptionData[] = [];
    const putOptions: OptionData[] = [];

    // Generate mock options around spot price
    for (let i = -5; i <= 5; i++) {
      const strike = Math.round((spotPrice + (i * (spotPrice * 0.01))) / 50) * 50;

      // Mock call option
      callOptions.push({
        symbol: `${symbol}${strike}CE`,
        strikePrice: strike,
        optionType: 'CE',
        expiryDate: '2024-12-26',
        ltp: Math.max(1, spotPrice - strike + Math.random() * 100),
        bid: 0,
        ask: 0,
        volume: Math.floor(Math.random() * 10000),
        openInterest: Math.floor(Math.random() * 50000),
        impliedVolatility: 15 + Math.random() * 20,
        delta: 0.3 + Math.random() * 0.4,
        gamma: 0.001 + Math.random() * 0.01,
        theta: -0.5 - Math.random() * 2,
        vega: 0.1 + Math.random() * 0.3,
        rho: 0.01 + Math.random() * 0.05,
        change: -50 + Math.random() * 100,
        changePercent: -5 + Math.random() * 10,
      });

      // Mock put option
      putOptions.push({
        symbol: `${symbol}${strike}PE`,
        strikePrice: strike,
        optionType: 'PE',
        expiryDate: '2024-12-26',
        ltp: Math.max(1, strike - spotPrice + Math.random() * 100),
        bid: 0,
        ask: 0,
        volume: Math.floor(Math.random() * 10000),
        openInterest: Math.floor(Math.random() * 50000),
        impliedVolatility: 15 + Math.random() * 20,
        delta: -0.3 - Math.random() * 0.4,
        gamma: 0.001 + Math.random() * 0.01,
        theta: -0.5 - Math.random() * 2,
        vega: 0.1 + Math.random() * 0.3,
        rho: -0.01 - Math.random() * 0.05,
        change: -50 + Math.random() * 100,
        changePercent: -5 + Math.random() * 10,
      });
    }

    return {
      underlyingSymbol: symbol,
      underlyingPrice: spotPrice,
      expiryDate: '2024-12-26',
      callOptions,
      putOptions,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Get options chain data for a symbol
   * Updated for Fyers API v3
   */
  async getOptionsChain(symbol: string, expiryDate?: string): Promise<OptionsChainData> {
    if (!this.isConfigured()) {
      console.warn('Fyers API not configured, using mock data');
      return this.generateMockOptionsData(symbol);
    }

    try {
      // Format symbol for Fyers API (e.g., NSE:NIFTY50-INDEX, NSE:RELIANCE-EQ)
      const formattedSymbol = this.formatSymbol(symbol);

      // First get the current price
      const currentPrice = await this.getQuote(symbol);

      // Get options chain - Updated endpoint for v3
      const response: AxiosResponse = await this.api.get('/data/optchain', {
        params: {
          symbol: formattedSymbol,
          ...(expiryDate && { expiry: expiryDate })
        },
      });

      if (response.data.s !== 'ok' || !response.data.d) {
        throw new Error(response.data.message || ERROR_MESSAGES.NO_DATA);
      }

      return this.transformOptionsChainData(response.data.d, symbol, currentPrice);
    } catch (error) {
      console.error('Error fetching options chain:', error);
      console.warn('Falling back to mock data due to API error');

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          console.error('Invalid access token. Using mock data.');
        }
        if (error.response?.status === 403) {
          console.error('Access forbidden. Using mock data.');
        }
      }

      // Return mock data as fallback
      return this.generateMockOptionsData(symbol);
    }
  }

  /**
   * Get current market price for a symbol
   * Updated for Fyers API v3
   */
  async getQuote(symbol: string): Promise<number> {
    try {
      const formattedSymbol = this.formatSymbol(symbol);
      const response: AxiosResponse = await this.api.get('/data/quotes', {
        params: { symbols: formattedSymbol },
      });

      if (response.data.s !== 'ok' || !response.data.d) {
        throw new Error(ERROR_MESSAGES.NO_DATA);
      }

      // Updated response structure for v3
      const quoteData = response.data.d[formattedSymbol];
      return quoteData?.v?.lp || quoteData?.lp || 0;
    } catch (error) {
      console.error('Error fetching quote:', error);
      // Return mock price as fallback
      const mockPrices: Record<string, number> = {
        'NIFTY': 24500,
        'BANKNIFTY': 52000,
        'FINNIFTY': 22000,
        'RELIANCE': 3500,
        'TCS': 4200,
        'HDFCBANK': 1800,
      };
      return mockPrices[symbol] || 1000;
    }
  }

  /**
   * Get historical data for analysis
   */
  async getHistoricalData(
    symbol: string,
    resolution: string = '1D',
    fromDate: string,
    toDate: string
  ): Promise<Record<string, unknown>> {
    try {
      const formattedSymbol = this.formatSymbol(symbol);
      const response: AxiosResponse = await this.api.get('/data/history', {
        params: {
          symbol: formattedSymbol,
          resolution,
          date_format: '1',
          range_from: fromDate,
          range_to: toDate,
        },
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching historical data:', error);
      throw error;
    }
  }

  /**
   * Format symbol for Fyers API v3
   */
  private formatSymbol(symbol: string): string {
    // Handle index symbols - Updated for v3
    if (symbol === 'NIFTY') {
      return 'NSE:NIFTY50-INDEX';
    }
    if (symbol === 'BANKNIFTY') {
      return 'NSE:BANKNIFTY-INDEX';
    }
    if (symbol === 'FINNIFTY') {
      return 'NSE:FINNIFTY-INDEX';
    }

    // Handle equity symbols
    return `NSE:${symbol}-EQ`;
  }

  /**
   * Transform Fyers API response to our data structure
   * Updated for v3 response format
   */
  private transformOptionsChainData(data: Record<string, unknown>, symbol: string, spotPrice: number): OptionsChainData {
    const callOptions: OptionData[] = [];
    const putOptions: OptionData[] = [];

    // Process options chain data - Updated for v3 format
    if (data && typeof data === 'object') {
      // v3 API returns data in a different structure
      Object.keys(data).forEach((key) => {
        const option = data[key] as Record<string, unknown>;
        if (option && typeof option === 'object') {
          const optionData: OptionData = {
            symbol: (option.fyToken as string) || key,
            strikePrice: (option.strikePrice as number) || 0,
            optionType: (option.optionType as string) === 'CE' ? 'CE' : 'PE',
            expiryDate: (option.expiry as string) || '',
            ltp: (option.ltp as number) || 0,
            bid: (option.bid as number) || 0,
            ask: (option.ask as number) || 0,
            volume: (option.volume as number) || 0,
            openInterest: (option.oi as number) || 0,
            impliedVolatility: (option.iv as number) || 0,
            delta: (option.delta as number) || 0,
            gamma: (option.gamma as number) || 0,
            theta: (option.theta as number) || 0,
            vega: (option.vega as number) || 0,
            rho: (option.rho as number) || 0,
            change: (option.ch as number) || 0,
            changePercent: (option.chp as number) || 0,
          };

          if ((option.optionType as string) === 'CE') {
            callOptions.push(optionData);
          } else if ((option.optionType as string) === 'PE') {
            putOptions.push(optionData);
          }
        }
      });
    }

    // Sort by strike price
    callOptions.sort((a, b) => a.strikePrice - b.strikePrice);
    putOptions.sort((a, b) => a.strikePrice - b.strikePrice);

    return {
      underlyingSymbol: symbol,
      underlyingPrice: spotPrice,
      expiryDate: callOptions[0]?.expiryDate || putOptions[0]?.expiryDate || '',
      callOptions,
      putOptions,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Get available expiry dates for a symbol
   */
  async getExpiryDates(symbol: string): Promise<string[]> {
    try {
      const formattedSymbol = this.formatSymbol(symbol);
      const response: AxiosResponse = await this.api.get('/data/optchain', {
        params: { symbol: formattedSymbol, get_expiry: 1 },
      });

      if (response.data.s !== 'ok' || !response.data.data) {
        return [];
      }

      return response.data.data.expiry_dates || [];
    } catch (error) {
      console.error('Error fetching expiry dates:', error);
      return [];
    }
  }
}

// Export singleton instance
export const fyersService = new FyersService();
export default fyersService;
