import React, { useState, useMemo } from 'react';
import {
  <PERSON>,
  CardContent,
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
  InputAdornment,
  ToggleButton,
  ToggleButtonGroup,
} from '@mui/material';
import {
  Search,
  TrendingUp,
  TrendingDown,
} from '@mui/icons-material';
import type { OptionsChainData, OptionData, StockSymbol } from '../types';

interface OptionsChainProps {
  data: OptionsChainData;
  selectedStock: StockSymbol | null;
}

type ViewMode = 'all' | 'calls' | 'puts';

const OptionsChain: React.FC<OptionsChainProps> = ({ data }) => {
  const [viewMode, setViewMode] = useState<ViewMode>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<keyof OptionData>('strikePrice');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Filter and sort options data
  const filteredData = useMemo(() => {
    let options: OptionData[] = [];
    
    switch (viewMode) {
      case 'calls':
        options = data.callOptions;
        break;
      case 'puts':
        options = data.putOptions;
        break;
      default:
        options = [...data.callOptions, ...data.putOptions];
    }

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      options = options.filter(option =>
        option.strikePrice.toString().includes(term) ||
        option.symbol.toLowerCase().includes(term)
      );
    }

    // Apply sorting
    options.sort((a, b) => {
      const aValue = a[sortBy];
      const bValue = b[sortBy];
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
      }
      
      return sortOrder === 'asc' 
        ? String(aValue).localeCompare(String(bValue))
        : String(bValue).localeCompare(String(aValue));
    });

    return options;
  }, [data, viewMode, searchTerm, sortBy, sortOrder]);

  // Handle view mode change
  const handleViewModeChange = (_event: React.MouseEvent<HTMLElement>, newMode: ViewMode) => {
    if (newMode !== null) {
      setViewMode(newMode);
    }
  };

  // Handle sort
  const handleSort = (column: keyof OptionData) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  // Format number with appropriate decimals
  const formatNumber = (value: number, decimals: number = 2): string => {
    return value.toFixed(decimals);
  };

  // Get color for change percentage
  const getChangeColor = (change: number): 'success' | 'error' | 'default' => {
    if (change > 0) return 'success';
    if (change < 0) return 'error';
    return 'default';
  };

  // Get ITM/OTM status
  const getMoneyness = (option: OptionData): { status: string; color: 'success' | 'warning' | 'error' } => {
    const spotPrice = data.underlyingPrice;
    const strike = option.strikePrice;
    
    if (option.optionType === 'CE') {
      if (spotPrice > strike) return { status: 'ITM', color: 'success' };
      if (Math.abs(spotPrice - strike) <= spotPrice * 0.02) return { status: 'ATM', color: 'warning' };
      return { status: 'OTM', color: 'error' };
    } else {
      if (spotPrice < strike) return { status: 'ITM', color: 'success' };
      if (Math.abs(spotPrice - strike) <= spotPrice * 0.02) return { status: 'ATM', color: 'warning' };
      return { status: 'OTM', color: 'error' };
    }
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6">
            Options Chain - {data.underlyingSymbol}
          </Typography>
          <Chip
            icon={<TrendingUp />}
            label={`Spot: ₹${formatNumber(data.underlyingPrice)}`}
            color="primary"
          />
        </Box>

        {/* Controls */}
        <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap', alignItems: 'center' }}>
          {/* View Mode Toggle */}
          <ToggleButtonGroup
            value={viewMode}
            exclusive
            onChange={handleViewModeChange}
            size="small"
          >
            <ToggleButton value="all">All</ToggleButton>
            <ToggleButton value="calls">Calls</ToggleButton>
            <ToggleButton value="puts">Puts</ToggleButton>
          </ToggleButtonGroup>

          {/* Search */}
          <TextField
            size="small"
            placeholder="Search strikes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 200 }}
          />

          <Typography variant="body2" color="text.secondary">
            {filteredData.length} options
          </Typography>
        </Box>

        {/* Options Table */}
        <TableContainer component={Paper} sx={{ maxHeight: 600 }}>
          <Table stickyHeader size="small">
            <TableHead>
              <TableRow>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                       onClick={() => handleSort('strikePrice')}>
                    Strike
                    {sortBy === 'strikePrice' && (
                      sortOrder === 'asc' ? <TrendingUp /> : <TrendingDown />
                    )}
                  </Box>
                </TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="right">
                  <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer', justifyContent: 'flex-end' }}
                       onClick={() => handleSort('ltp')}>
                    LTP
                    {sortBy === 'ltp' && (
                      sortOrder === 'asc' ? <TrendingUp /> : <TrendingDown />
                    )}
                  </Box>
                </TableCell>
                <TableCell align="right">Change</TableCell>
                <TableCell align="right">
                  <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer', justifyContent: 'flex-end' }}
                       onClick={() => handleSort('volume')}>
                    Volume
                    {sortBy === 'volume' && (
                      sortOrder === 'asc' ? <TrendingUp /> : <TrendingDown />
                    )}
                  </Box>
                </TableCell>
                <TableCell align="right">
                  <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer', justifyContent: 'flex-end' }}
                       onClick={() => handleSort('openInterest')}>
                    OI
                    {sortBy === 'openInterest' && (
                      sortOrder === 'asc' ? <TrendingUp /> : <TrendingDown />
                    )}
                  </Box>
                </TableCell>
                <TableCell align="right">IV</TableCell>
                <TableCell align="right">Delta</TableCell>
                <TableCell align="right">Gamma</TableCell>
                <TableCell align="right">Theta</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredData.map((option, index) => {
                const moneyness = getMoneyness(option);
                return (
                  <TableRow key={`${option.symbol}-${index}`} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {option.strikePrice}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={option.optionType}
                        color={option.optionType === 'CE' ? 'success' : 'error'}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={moneyness.status}
                        color={moneyness.color}
                        size="small"
                        variant="filled"
                      />
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" fontWeight="medium">
                        ₹{formatNumber(option.ltp)}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Chip
                        label={`${option.changePercent > 0 ? '+' : ''}${formatNumber(option.changePercent)}%`}
                        color={getChangeColor(option.changePercent)}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2">
                        {option.volume.toLocaleString()}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2">
                        {option.openInterest.toLocaleString()}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2">
                        {formatNumber(option.impliedVolatility)}%
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2">
                        {formatNumber(option.delta, 3)}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2">
                        {formatNumber(option.gamma, 4)}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" color={option.theta < 0 ? 'error.main' : 'text.primary'}>
                        {formatNumber(option.theta, 3)}
                      </Typography>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>

        {filteredData.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="body1" color="text.secondary">
              No options data found for the current filters
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default OptionsChain;
