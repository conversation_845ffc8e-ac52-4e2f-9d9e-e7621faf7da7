/* Options Chain Analyzer App Styles */

/* App-specific styles */
.app-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* Custom styles for options chain table */
.options-table-container {
  max-height: 600px;
  overflow-y: auto;
}

/* Strike price highlighting */
.strike-price-cell {
  font-weight: 600;
  color: #1976d2;
}

/* ITM/OTM status colors */
.itm-option {
  background-color: rgba(76, 175, 80, 0.1);
}

.atm-option {
  background-color: rgba(255, 193, 7, 0.1);
}

.otm-option {
  background-color: rgba(244, 67, 54, 0.1);
}

/* AI analysis cards */
.ai-recommendation-card {
  border-left: 4px solid #1976d2;
  transition: all 0.3s ease;
}

.ai-recommendation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
