/**
 * Fyers Access Token Generator
 * 
 * This script helps generate access tokens for Fyers API v3
 * Run this script daily to get fresh access tokens
 * 
 * Usage:
 * 1. Set your APP_ID and SECRET_ID in .env file
 * 2. Run: node scripts/generateFyersToken.js
 * 3. Follow the prompts to complete OAuth flow
 */

import crypto from 'crypto';
import readline from 'readline';
import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const APP_ID = process.env.VITE_FYERS_APP_ID;
const SECRET_ID = process.env.VITE_FYERS_SECRET_ID;
const REDIRECT_URI = 'https://www.google.co.in/';

if (!APP_ID || !SECRET_ID) {
  console.error('❌ Error: APP_ID and SECRET_ID must be set in .env file');
  process.exit(1);
}

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

function generateAppIdHash(appId, secretId) {
  const data = `${appId}:${secretId}`;
  return crypto.createHash('sha256').update(data).digest('hex');
}

async function generateAccessToken() {
  console.log('🚀 Fyers Access Token Generator\n');
  
  try {
    // Step 1: Generate authorization URL
    const authUrl = `https://api-t1.fyers.in/api/v3/generate-authcode?client_id=${APP_ID}&redirect_uri=${encodeURIComponent(REDIRECT_URI)}&response_type=code&state=sample_state`;
    
    console.log('📋 Step 1: Authorization');
    console.log('Please visit the following URL to authorize the application:');
    console.log(`\n${authUrl}\n`);
    console.log('After authorization, you will be redirected to Google with a URL that looks like:');
    console.log(`${REDIRECT_URI}?auth_code=XXXXXX&state=sample_state\n`);
    
    // Step 2: Get auth code from user
    const authCode = await question('Enter the auth_code from the redirect URL: ');
    
    if (!authCode || authCode.trim() === '') {
      throw new Error('Auth code is required');
    }
    
    console.log('\n🔄 Generating access token...');
    
    // Step 3: Generate access token
    const appIdHash = generateAppIdHash(APP_ID, SECRET_ID);
    
    const tokenResponse = await fetch('https://api-t1.fyers.in/api/v3/validate-authcode', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        grant_type: 'authorization_code',
        appIdHash: appIdHash,
        code: authCode.trim()
      })
    });
    
    const tokenData = await tokenResponse.json();
    
    if (tokenData.s === 'ok' && tokenData.access_token) {
      console.log('\n✅ Success! Access token generated:');
      console.log(`\nAccess Token: ${tokenData.access_token}`);
      console.log(`\n📝 Update your .env file with:`);
      console.log(`VITE_FYERS_ACCESS_TOKEN=${tokenData.access_token}`);
      
      if (tokenData.refresh_token) {
        console.log(`\n🔄 Refresh Token (for future use): ${tokenData.refresh_token}`);
      }
      
      console.log('\n⏰ Note: This token is valid for 24 hours');
      console.log('💡 Tip: Run this script daily or implement automated token refresh');
      
    } else {
      console.error('\n❌ Error generating access token:');
      console.error(JSON.stringify(tokenData, null, 2));
    }
    
  } catch (error) {
    console.error('\n❌ Error:', error.message);
  } finally {
    rl.close();
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\n👋 Process terminated by user');
  rl.close();
  process.exit(0);
});

// Start the token generation process
generateAccessToken();
