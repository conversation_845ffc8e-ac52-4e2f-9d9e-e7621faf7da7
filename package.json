{"name": "options-chain-analyzer", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "generate-token": "node scripts/generateFyersToken.js"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@mui/x-data-grid": "^8.5.1", "@types/node": "^24.0.0", "axios": "^1.9.0", "crypto-browserify": "^3.12.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "node-fetch": "^3.3.2", "path-browserify": "^1.0.1", "react": "^19.1.0", "react-dom": "^19.1.0", "recharts": "^2.15.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}